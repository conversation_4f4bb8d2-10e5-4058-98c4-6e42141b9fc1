<template>
  <NuxtLayout>
    <NuxtPage />
    <TheFooter />
    <SeoSchema />
  </NuxtLayout>
</template>

<script setup lang="ts">
import { onMounted, ref, nextTick, watch, reactive, provide } from 'vue'
import { cookieUtil } from '~/utils/cookie'
import { useAuth } from '~/composables/useAuth'

// 类型定义
interface UserState {
  isLoggedIn: boolean
  credits: number
  userId: string | null
}

interface User {
  id: string
  email: string
  credits?: number
  [key: string]: any
}

// 声明 google 全局变量
declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void
          renderButton: (element: HTMLElement | null, options: any) => void
          disableAutoSelect: () => void
          revoke: (email: string, callback: () => void) => void
        }
      }
    }
    handleCredentialResponse?: (response: any) => void
  }
}

const { locale, setLocale, t } = useI18n()
const localePath = useLocalePath()
const config = useRuntimeConfig()
const googleClientId = config.public.GOOGLE_CLIENT_ID
const isGoogleScriptLoaded = ref(false)
const user = ref<User | null>(null)
const auth = useAuth()

const userState = reactive<UserState>({
  isLoggedIn: false,
  credits: 3,
  userId: null
})

provide('userState', userState)

// New functions for local storage
function saveUserState() {
  localStorage.setItem('userState', JSON.stringify(userState))
}

function loadUserState() {
  const savedState = localStorage.getItem('userState')
  if (savedState) {
    const parsedState = JSON.parse(savedState)
    Object.assign(userState, parsedState)
    return true
  }
  return false
}

function clearUserState() {
  localStorage.removeItem('userState')
}

// Update the handleCredentialResponse function
async function handleCredentialResponse(response: { credential: string }) {
  console.log("Encoded JWT ID token: " + response.credential);
  try {
    const res = await fetch(`${config.public.apiBase}/api/auth/google-login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ credential: response.credential }),
    });
    const data = await res.json();
    if (data.success) {
      // 更新用户状态
      user.value = data.user;
      userState.isLoggedIn = true;
      userState.credits = data.user.credits || 3;
      userState.userId = data.user.id;
      
      // 保存状态 - 添加更多cookie选项
      cookieUtil.setCookie('xToken', data.user.id, {
        expires: 7,
        path: '/',
        secure: window.location.protocol === 'https:',
        sameSite: 'lax',
        domain: window.location.hostname === 'localhost' ? 'localhost' : '.fillify.tech'
      });
      saveUserState();
      
      // 等待状态更新完成
      await nextTick();
      console.log('User logged in:', user.value);
      
      // 检查当前路由并导航
      const currentRoute = useRoute();
      if (currentRoute.path.includes('/signin')) {
        await nextTick();
        await auth.fetchUserData();
        
        // 使用 try-catch 确保导航成功
        try {
          const currentLang = locale.value;
          const dashboardPath = currentLang === 'en' ? '/dashboard' : `/${currentLang}/dashboard`;
          await navigateTo(dashboardPath, { replace: true });
        } catch (navError) {
          console.error('Navigation error:', navError);
          // 如果导航失败，使用 window.location
          window.location.href = localePath('/dashboard');
        }
      }
    } else {
      console.error('Failed to log in:', data.error);
    }
  } catch (error) {
    console.error('Error during login:', error);
  }
}

// Update the signOut function
async function signOut() {
  // 定义可能的域名配置
  const domains: (string | undefined)[] = [
    undefined, // 默认域名
    'localhost',
    'fillify.tech',
    '.fillify.tech'
  ];

  try {
    // 调用登出 API
    const res = await fetch(`${config.public.apiBase}/api/auth/logout`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${cookieUtil.getCookie('xToken')}`
      }
    });

    if (!res.ok) {
      throw new Error(`Logout failed with status: ${res.status}`);
    }

    // 无论API调用是否成功，都清理本地状态
    if (window.google?.accounts?.id && user.value?.email) {
      window.google.accounts.id.disableAutoSelect();
      window.google.accounts.id.revoke(user.value.email, () => {
        console.log('Revoked Google authentication');
      });
    }

    // 清理所有状态
    user.value = null;
    userState.isLoggedIn = false;
    userState.credits = 0;
    userState.userId = null;

    // 清理 cookie (同时清理所有可能的域名配置)
    domains.forEach((domain: string | undefined) => {
      cookieUtil.removeCookie('xToken', {
        path: '/',
        domain,
        secure: window.location.protocol === 'https:',
        sameSite: 'lax'
      });
    });
    
    // 清理本地存储
    clearUserState();
    localStorage.clear();
    
    console.log('User signed out completely');
    
    // 重新渲染登录按钮
    await nextTick(() => {
      renderGoogleSignInButton();
    });

    // 强制刷新页面以确保状态完全清理
    window.location.href = '/';
  } catch (error) {
    console.error('Error during sign out:', error);
    // 即使出错也清理本地状态
    clearUserState();
    localStorage.clear();
    domains.forEach((domain: string | undefined) => {
      cookieUtil.removeCookie('xToken', {
        path: '/',
        domain,
        secure: window.location.protocol === 'https:',
        sameSite: 'lax'
      });
    });
    // 强制刷新页面
    window.location.href = '/';
  }
}

function renderGoogleSignInButton() {
  if (window.google?.accounts?.id) {
    window.google.accounts.id.initialize({
      client_id: googleClientId,
      callback: handleCredentialResponse
    })
    const buttonElement = document.getElementById('googleSignInButton')
    if (buttonElement) {
      window.google.accounts.id.renderButton(
        buttonElement,
        { theme: 'outline', size: 'large' }
      )
    }
    isGoogleScriptLoaded.value = true
  } else {
    console.warn('Google Sign-In script not fully loaded yet')
  }
}

// Add fetchUserData function
async function fetchUserData() {
  try {
    const res = await fetch(`${config.public.apiBase}/api/users/get-user`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId: userState.userId }),
    });
    const data = await res.json();
    if (data.success) {
      user.value = data.user;
      userState.isLoggedIn = true;
      userState.credits = data.user.credits;
      console.log('User data fetched:', user.value);
    } else {
      console.error('Failed to fetch user data:', data.error);
      clearUserState();
    }
  } catch (error) {
    console.error('Error fetching user data:', error);
    clearUserState();
  }
}

onMounted(() => {
  // 先尝试从localStorage加载状态
  const hasLocalState = loadUserState()
  
  // 检查cookie中是否存在xToken
  const xToken = cookieUtil.getCookie('xToken')
  
  // 只有在有xToken且本地状态无效时才获取用户数据
  if (xToken && !hasLocalState) {
    fetchUserData()
  }

  // Make the callback function available globally
  window.handleCredentialResponse = handleCredentialResponse

  // Check for Google Sign-In script load
  const checkGoogleScriptLoaded = setInterval(() => {
    if (window.google && window.google.accounts && window.google.accounts.id) {
      clearInterval(checkGoogleScriptLoaded)
      renderGoogleSignInButton()
    }
  }, 100)

  renderGoogleSignInButton()
})

// Add a watcher for isGoogleScriptLoaded
watch(isGoogleScriptLoaded, (newValue) => {
  if (newValue) {
    renderGoogleSignInButton()
  }
})

useHead({
  script: [
    {// google sign in
      src: 'https://accounts.google.com/gsi/client',
      async: true,
      defer: true,
    },
    {// supabase
      src: 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2',
      onload: () => { window.dispatchEvent(new Event('supabase-loaded')); }
    },
  ],
  htmlAttrs: {
    lang: locale.value
  }
})
</script>
