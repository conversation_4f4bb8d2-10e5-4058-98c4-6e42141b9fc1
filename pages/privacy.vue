<template>
  <div class="min-h-screen bg-gradient-to-b from-blue-50 to-white">
    <main class="pt-24">
      <!-- 标题区域 -->
      <div class="container mx-auto px-4">
        <div class="flex items-center justify-center mb-12 relative">
          <button 
            class="absolute left-0 text-gray-600 hover:text-gray-900 transition-colors"
            @click="router.back()"
          >
            <ArrowLeft class="h-6 w-6" />
          </button>
          <h1 class="text-4xl md:text-5xl font-bold text-gray-900">
            {{ t('privacy.title') }}
          </h1>
        </div>
        
        <!-- 内容区域 -->
        <div class="max-w-4xl mx-auto space-y-12 bg-white rounded-2xl shadow-sm p-8 md:p-12 mb-20">
          <!-- 添加更新日期 -->
          <div class="text-gray-500 text-lg mb-8">{{ t('privacy.lastUpdated', { date: '2025-02-01' }) }}</div>

          <section>
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">1. Information Processing</h2>
            <p class="text-lg text-gray-600 leading-relaxed">
              Fillify processes all form data locally in your browser. We do not collect, store, or transmit the content of forms you fill using our extension. Your API keys for AI providers are stored securely in your browser's local storage and are never sent to our servers.
            </p>
          </section>

          <section>
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">2. Data We Collect</h2>
            <p class="text-lg text-gray-600 leading-relaxed">
              We collect minimal data necessary for the operation of our service:
            </p>
            <ul class="list-disc pl-6 mt-4 space-y-2 text-lg text-gray-600">
              <li>Basic usage statistics (number of forms filled, success rates)</li>
              <li>Error reports (when you choose to send them)</li>
              <li>Extension installation and update events</li>
            </ul>
          </section>

          <section>
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">3. Third-Party Services</h2>
            <p class="text-lg text-gray-600 leading-relaxed">
              When you use Fillify with AI providers (OpenAI, Anthropic Claude, Google Gemini, or Moonshot AI), your form content is processed by your chosen provider. These interactions are governed by their respective privacy policies. We do not have access to these interactions or your API keys.
            </p>
          </section>

          <section>
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">4. Data Security</h2>
            <p class="text-lg text-gray-600 leading-relaxed">
              We implement several security measures to protect your data:
            </p>
            <ul class="list-disc pl-6 mt-4 space-y-2 text-lg text-gray-600">
              <li>Local storage encryption for sensitive data</li>
              <li>Secure HTTPS connections for any server communication</li>
              <li>Regular security audits and updates</li>
            </ul>
          </section>

          <section>
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">5. Your Rights</h2>
            <p class="text-lg text-gray-600 leading-relaxed">
              You have the right to:
            </p>
            <ul class="list-disc pl-6 mt-4 space-y-2 text-lg text-gray-600">
              <li>Access your locally stored data</li>
              <li>Delete your data from local storage</li>
              <li>Opt-out of any data collection</li>
              <li>Request information about your data</li>
            </ul>
          </section>

          <section>
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">6. Changes to Privacy Policy</h2>
            <p class="text-lg text-gray-600 leading-relaxed">
              We may update this privacy policy from time to time. We will notify you of any material changes by updating the "Last updated" date at the top of this page and, where appropriate, notify you via the extension or email.
            </p>
          </section>

          <section>
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">7. Contact Us</h2>
            <p class="text-lg text-gray-600 leading-relaxed">
              If you have any questions about this privacy policy, please contact us at <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800"><EMAIL></a>.
            </p>
          </section>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft } from 'lucide-vue-next'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useSeo } from '~/composables/useSeo'

const router = useRouter()
const { t } = useI18n()
const { setSeoMeta } = useSeo()

// Set SEO meta tags for privacy page
setSeoMeta('/privacy')
</script>

<style scoped>
.animate-fade-in {
  opacity: 0;
  animation: fadeIn 0.8s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>