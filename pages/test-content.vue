<template>
  <div class="min-h-screen bg-gradient-to-b from-blue-50 to-white">
    <main class="pt-24 pb-16">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <h1 class="text-4xl font-bold mb-8">Content Test Page</h1>
          
          <div v-if="pending" class="text-center py-8">
            <p>Loading...</p>
          </div>
          
          <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <p>Error: {{ error.message }}</p>
          </div>
          
          <div v-else>
            <div class="mb-8">
              <h2 class="text-2xl font-bold mb-4">All Content Files:</h2>
              <ul class="bg-white rounded-lg shadow p-4">
                <li v-for="file in files" :key="file._id" class="py-2 border-b border-gray-200 last:border-b-0">
                  <span class="font-medium">{{ file._path }}</span> - {{ file.title }}
                </li>
              </ul>
            </div>
            
            <div>
              <h2 class="text-2xl font-bold mb-4">Query Result:</h2>
              <pre class="bg-gray-100 p-4 rounded-lg overflow-auto">{{ JSON.stringify(files, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
// 测试内容查询功能
const { data: files, pending, error } = await useAsyncData('content-test', async () => {
  try {
    console.log('Querying content...')
    const result = await queryContent().find()
    console.log('Query result:', result)
    return result
  } catch (err) {
    console.error('Error querying content:', err)
    throw err
  }
})
</script>