<template>
  <div class="min-h-screen bg-gradient-to-b from-blue-50 to-white">
    <main class="pt-24 pb-16">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <article class="bg-white rounded-2xl shadow-sm p-6 md:p-8">
            <div class="mb-6">
              <NuxtLink 
                :to="localePath('/blog')" 
                class="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors mb-6"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                {{ t('blog.backToBlog') }}
              </NuxtLink>
            </div>
            
            <header class="mb-8" v-if="article">
              <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {{ article.title }}
              </h1>
              <div class="flex items-center text-gray-500 text-sm">
                <span>{{ formatDate(article.date) }}</span>
              </div>
            </header>
            
            <div class="prose prose-lg max-w-none" v-if="article">
              <ContentRenderer :value="article" />
            </div>
            
            <div v-else class="text-center py-12">
              <p class="text-gray-500">Article not found</p>
            </div>
          </article>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { useLocalePath } from '#i18n'

const { t } = useI18n()
const localePath = useLocalePath()
const route = useRoute()

// Set default SEO meta tags
useHead({
  title: t('blog.meta.title'),
  meta: [
    { name: 'description', content: t('blog.meta.description') },
  ],
})

// Fetch the article based on the route
const { data: article } = await useAsyncData(`article-${route.path}`, async () => {
  try {
    // Remove the leading slash and 'blog/' from the path
    const slug = route.path.replace(/^\/blog\//, '')
    console.log('Fetching article with slug:', slug) // 添加日志查看 slug
    const result = await queryContent(slug).findOne()
    console.log('Fetched article:', result) // 添加日志查看获取的文章
    return result
  } catch (error) {
    console.error('Error fetching article:', error)
    throw createError({ statusCode: 404, statusMessage: 'Article not found' })
  }
})

// Format date for display
const formatDate = (dateString) => {
  const options = { year: 'numeric', month: 'long', day: 'numeric' }
  return new Date(dateString).toLocaleDateString('en-US', options)
}

// Set page title and description from the article
if (article.value) {
  useHead({
    title: `${article.value.title} - ${t('blog.meta.title')}`,
    meta: [
      { name: 'description', content: article.value.description },
    ],
  })
}
</script>

<style scoped>
/* Add any additional styling here if needed */
</style>