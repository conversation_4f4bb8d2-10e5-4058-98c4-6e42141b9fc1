<template>
  <div class="min-h-screen bg-gradient-to-b from-blue-50 to-white">
    <main class="pt-24 pb-16">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <h1 class="text-4xl md:text-5xl font-bold text-center text-gray-900 mb-4">
            {{ t('blog.title') }}
          </h1>
          <p class="text-xl text-gray-600 text-center mb-12">
            {{ t('blog.subtitle') }}
          </p>
          
          <div class="space-y-8">
            <article 
              v-for="post in posts" 
              :key="post._path"
              class="bg-white rounded-2xl shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300"
            >
              <NuxtLink :to="localePath(post._path)" class="block p-6 md:p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
                  {{ post.title }}
                </h2>
                <p class="text-gray-600 mb-4 line-clamp-2">
                  {{ post.description }}
                </p>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-500">
                    {{ formatDate(post.date) }}
                  </span>
                  <span class="text-blue-600 font-medium text-sm">
                    Read more →
                  </span>
                </div>
              </NuxtLink>
            </article>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { useLocalePath } from '#i18n'

const { t } = useI18n()
const localePath = useLocalePath()

// Set SEO meta tags for blog page
useHead({
  title: t('blog.meta.title'),
  meta: [
    { 
      name: 'description', 
      content: t('blog.meta.description')
    },
  ],
})

// Fetch blog posts
const { data: posts } = await useAsyncData('blog-posts', async () => {
  try {
    // Use the built-in queryContent function from Nuxt Content
    const result = await queryContent().sort({ date: -1 }).find()
    console.log('Fetched posts:', result) // 添加日志查看获取的数据
    return result
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    return []
  }
})

// Format date for display
const formatDate = (dateString) => {
  const options = { year: 'numeric', month: 'long', day: 'numeric' }
  return new Date(dateString).toLocaleDateString('en-US', options)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>