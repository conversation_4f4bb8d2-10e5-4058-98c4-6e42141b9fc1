<template>
  <div class="min-h-screen bg-gradient-to-b from-blue-50 to-white">
    <ClientOnly>
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white -mt-[60px] pt-[60px] h-[calc(50vh+60px)] flex items-center">
      <div class="container mx-auto px-4">
        <div class="flex items-center gap-8">
          <!-- Avatar -->
          <div class="relative">
            <div v-if="user?.picture_url" class="w-24 h-24 rounded-full overflow-hidden">
              <img :src="user.picture_url" :alt="user.full_name || 'User avatar'" class="w-full h-full object-cover">
            </div>
            <div v-else class="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
              <UserCircle class="w-14 h-14" />
            </div>
          </div>
          
          <!-- User Info -->
          <div class="flex-1">
            <h1 class="text-2xl font-medium mb-2">{{ user?.full_name || 'User' }}</h1>
            <p class="text-blue-100 text-lg">{{ user?.email }}</p>
          </div>
          
          <!-- Settings Button -->
          <!-- <button 
            @click="openExtensionSettings" 
            class="bg-white/10 hover:bg-white/20 text-white px-5 py-2.5 rounded-lg transition duration-200 flex items-center gap-2"
          >
            <Settings class="w-5 h-5" />
            {{ t('dashboard.settings') }}
          </button> -->
        </div>
      </div>
    </div>

    <!-- Dashboard Content -->
    <div class="container mx-auto px-4 py-12">
      <div class="space-y-8">
        <!-- Subscription Info Card -->
        <div class="bg-white rounded-xl shadow-sm p-8 hover:shadow-md transition duration-200 min-h-[160px]">
          <h3 class="text-2xl font-bold text-gray-900 mb-6">{{ t('dashboard.currentPlan') }}</h3>
          <p class="text-xl text-gray-700">Free Plan</p>
        </div>

        <!-- Usage Card -->
        <!-- <div class="bg-white rounded-xl shadow-sm p-8 hover:shadow-md transition duration-200 min-h-[160px]">
          <h3 class="text-xl font-bold text-gray-900 mb-6">{{ t('dashboard.usageOverview') }}</h3>
          
          <div>
            <div class="flex justify-between text-sm mb-2">
              <span class="text-gray-600">{{ t('dashboard.creditsUsed') }}</span>
              <span class="text-gray-900 font-medium">{{ 3 - userState.credits }} / 3</span>
            </div>
            <div class="h-2 bg-gray-100 rounded-full overflow-hidden">
              <div 
                class="h-full bg-blue-600 rounded-full" 
                :style="{ width: ((3 - userState.credits) / 3 * 100) + '%' }"
              ></div>
            </div>
          </div>
        </div> -->
      </div>
    </div>
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import { Circle, Settings, UserCircle } from 'lucide-vue-next'
import { useAuth } from '~/composables/useAuth'
import { inject, onMounted, watch } from 'vue'
import { useHead } from 'unhead'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const { user, fetchUserData } = useAuth()
const userState = inject('userState') as {
  isLoggedIn: boolean
  credits: number
  userId: string | null
}

const openExtensionSettings = () => {
  window.open('chrome-extension://mhhcjilkgnegleamofnbeacpnblblkhn/settings.html', '_blank')
}

onMounted(async () => {
  await fetchUserData()
})

// 使用 watch 来更新标题
watch(() => t('dashboard.meta.title'), (newTitle) => {
  useHead({
    title: newTitle,
    meta: [
      { name: 'description', content: t('dashboard.meta.description') }
    ]
  })
}, { immediate: true })
</script>