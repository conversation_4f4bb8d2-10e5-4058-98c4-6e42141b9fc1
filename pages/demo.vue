<template>
  <div class="min-h-screen bg-gradient-to-b from-blue-50 to-white py-12">
    <div class="container mx-auto px-4">
      <!-- Page Title -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Information Posting Demo</h1>
        <p class="mt-2 text-gray-600">Fill out this form to post your information - Perfect for testing Fillify</p>
      </div>

      <!-- Main Form -->
      <div class="max-w-3xl mx-auto bg-white shadow-lg rounded-lg p-6">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Basic Information -->
          <div class="space-y-4">
            <h2 class="text-xl font-semibold text-gray-900 border-b pb-2">Basic Information</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Title -->
              <div class="col-span-2">
                <label class="block text-sm font-medium text-gray-700">Title</label>
                <input
                  type="text"
                  v-model="formData.title"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Enter a descriptive title"
                />
              </div>

              <!-- Name -->
              <div>
                <label class="block text-sm font-medium text-gray-700">Name</label>
                <input
                  type="text"
                  v-model="formData.name"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Your name"
                />
              </div>

              <!-- Phone -->
              <div>
                <label class="block text-sm font-medium text-gray-700">Phone Number</label>
                <input
                  type="tel"
                  v-model="formData.phone"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Your contact number"
                />
              </div>
            </div>
          </div>

          <!-- Detailed Information -->
          <div class="space-y-4">
            <h2 class="text-xl font-semibold text-gray-900 border-b pb-2">Detailed Information</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Category -->
              <div>
                <label class="block text-sm font-medium text-gray-700">Category</label>
                <input
                  type="text"
                  v-model="formData.category"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Enter category (e.g. Housing, Jobs, Services)"
                />
              </div>

              <!-- Price -->
              <div>
                <label class="block text-sm font-medium text-gray-700">Price</label>
                <input
                  type="text"
                  v-model="formData.price"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Enter price"
                />
              </div>

              <!-- Address -->
              <div class="col-span-2">
                <label class="block text-sm font-medium text-gray-700">Address</label>
                <input
                  type="text"
                  v-model="formData.address"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Enter location"
                />
              </div>

              <!-- Description -->
              <div class="col-span-2">
                <label class="block text-sm font-medium text-gray-700">Description</label>
                <textarea
                  v-model="formData.description"
                  rows="4"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Provide detailed information about your posting"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- Submit Buttons -->
          <div class="flex justify-end space-x-4">
            <button
              type="button"
              @click="resetForm"
              class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Reset
            </button>
            <button
              type="submit"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              :disabled="isSubmitting"
            >
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const isSubmitting = ref(false)

// Form data
const formData = ref({
  title: '',
  name: '',
  phone: '',
  category: '',
  price: '',
  address: '',
  description: ''
})

// Reset form
const resetForm = () => {
  formData.value = {
    title: '',
    name: '',
    phone: '',
    category: '',
    price: '',
    address: '',
    description: ''
  }
}

// Submit form
const handleSubmit = async () => {
  isSubmitting.value = true
  try {
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 1000))
    alert('Information submitted successfully!')
    resetForm()
  } catch (error) {
    alert('Failed to submit information. Please try again.')
  } finally {
    isSubmitting.value = false
  }
}

// SEO
useHead({
  title: 'Demo Form - Test Fillify Auto-Fill',
  meta: [
    { name: 'description', content: 'Try out Fillify\'s AI-powered form filling capabilities with this demo form' }
  ]
})
</script> 