<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'

const { t } = useI18n()
const route = useRoute()

// Get current locale
const locale = useI18n().locale.value

// Base URL - should match your site URL
const baseUrl = 'https://fillify.tech'

// Generate structured data based on current route
const getStructuredData = () => {
  const data = []

  // Common WebSite schema
  data.push({
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    'name': t('meta.title'),
    'url': baseUrl,
    'description': t('meta.description'),
    'inLanguage': locale,
    'publisher': {
      '@type': 'Organization',
      'name': 'Fillify',
      'url': baseUrl,
      'logo': {
        '@type': 'ImageObject',
        'url': `${baseUrl}/logo/logo-512.png`,
        'width': 512,
        'height': 512
      }
    }
  })

  // Specific schema based on route
  if (route.path === '/' || route.path === `/${locale}`) {
    // SoftwareApplication schema for homepage
    data.push({
      '@context': 'https://schema.org',
      '@type': 'SoftwareApplication',
      'name': 'Fillify',
      'url': baseUrl,
      'description': t('meta.description'),
      'applicationCategory': 'BrowserExtension',
      'operatingSystem': 'Chrome',
      'offers': {
        '@type': 'Offer',
        'price': '0',
        'priceCurrency': 'USD'
      },
      'featureList': [
        t('features.formFilling.title'),
        t('features.email.title'),
        t('features.bugReport.title'),
        t('features.aiProvider.title')
      ],
      'softwareVersion': '1.0',
      'aggregateRating': {
        '@type': 'AggregateRating',
        'ratingValue': '4.8',
        'ratingCount': '120',
        'bestRating': '5',
        'worstRating': '1'
      }
    })

    // FAQPage schema for homepage FAQ section
    const faqItems = [
      { question: t('faq.items.what.question'), answer: t('faq.items.what.answer') },
      { question: t('faq.items.types.question'), answer: t('faq.items.types.answer') },
      { question: t('faq.items.providers.question'), answer: t('faq.items.providers.answer') },
      { question: t('faq.items.privacy.question'), answer: t('faq.items.privacy.answer') },
      { question: t('faq.items.customize.question'), answer: t('faq.items.customize.answer') },
      { question: t('faq.items.languages.question'), answer: t('faq.items.languages.answer') }
    ]

    data.push({
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      'mainEntity': faqItems.map(item => ({
        '@type': 'Question',
        'name': item.question,
        'acceptedAnswer': {
          '@type': 'Answer',
          'text': item.answer
        }
      }))
    })
  }

  // Add BreadcrumbList for all pages except homepage
  if (!(route.path === '/' || route.path === `/${locale}`)) {
    let breadcrumbItems = []

    // Add homepage as first item
    breadcrumbItems.push({
      '@type': 'ListItem',
      'position': 1,
      'name': t('nav.home'), // Use translated 'Home'
      'item': baseUrl
    })

    // Determine current page name with a more robust approach
    let currentPageName = ''
    
    // Handle specific known pages
    if (route.path === '/terms') {
      currentPageName = t('terms.title')
    } else if (route.path === '/privacy') {
      currentPageName = t('privacy.title')
    } else if (route.path === '/dashboard') {
      currentPageName = t('dashboard.meta.title')
    } else if (route.path === '/signin') {
      currentPageName = t('signin.seo.title')
    } else {
      // For other pages, try to find a suitable title
      // 1. Check if there's a specific meta title for the page
      const pathName = route.path.substring(1) // Remove leading slash
      if (pathName && t(`${pathName}.meta.title`) !== `${pathName}.meta.title`) {
        currentPageName = t(`${pathName}.meta.title`)
      }
      // 2. Check if there's a title for the page
      else if (pathName && t(`${pathName}.title`) !== `${pathName}.title`) {
        currentPageName = t(`${pathName}.title`)
      }
      // 3. Fallback to a generic approach based on path
      else {
        const pathSegments = route.path.split('/').filter(segment => segment)
        if (pathSegments.length > 0) {
          // Capitalize the first letter of the last path segment
          const lastSegment = pathSegments[pathSegments.length - 1]
          currentPageName = lastSegment.charAt(0).toUpperCase() + lastSegment.slice(1)
        } else {
          currentPageName = 'Page' // Generic fallback
        }
      }
    }

    breadcrumbItems.push({
      '@type': 'ListItem',
      'position': breadcrumbItems.length + 1,
      'name': currentPageName,
      'item': `${baseUrl}${route.path}`
    })

    data.push({
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      'itemListElement': breadcrumbItems
    })
  }

  return data
}

// Computed property for structured data
const structuredData = computed(() => getStructuredData())
</script>

<template>
  <div v-for="(data, index) in structuredData" :key="index">
    <component is="script" type="application/ld+json" v-html="JSON.stringify(data)"></component>
  </div>
</template>