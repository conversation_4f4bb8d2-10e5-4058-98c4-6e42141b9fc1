import { useState } from 'nuxt/app'
import { computed } from 'vue'
import { cookieUtil } from '~/utils/cookie'

// 定义用户类型
type User = {
  id: string
  email: string
  full_name: string
  picture_url?: string
  credits?: number
}

// 定义可能的域名配置
const DOMAINS: (string | undefined)[] = [
  undefined, // 默认域名
  'localhost',
  'fillify.tech',
  '.fillify.tech'
]

export const useAuth = () => {
  const config = useRuntimeConfig()
  const apiBase = config.public.apiBase
  const user = useState<User | null>('user', () => null)
  const isLoggedIn = computed(() => !!user.value)

  // 清理所有认证状态
  const clearAuthState = () => {
    user.value = null
    DOMAINS.forEach(domain => {
      cookieUtil.removeCookie('xToken', {
        path: '/',
        domain,
        secure: window.location.protocol === 'https:',
        sameSite: 'lax'
      })
    })
    localStorage.removeItem('userState')
  }

  const signOut = async () => {
    try {
      // 调用登出 API
      const res = await fetch(`${apiBase}/api/auth/logout`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${cookieUtil.getCookie('xToken')}`
        }
      })

      if (!res.ok) {
        throw new Error(`Logout failed with status: ${res.status}`)
      }
      
      // 清理所有状态
      clearAuthState()
      
      // 强制刷新页面以确保状态完全清理
      window.location.href = '/'
    } catch (error) {
      console.error('Logout error:', error)
      // 即使出错也清理本地状态
      clearAuthState()
      window.location.href = '/'
    }
  }

  const fetchUserData = async () => {
    const xToken = cookieUtil.getCookie('xToken')
    if (!xToken) {
      clearAuthState()
      return
    }

    try {
      const res = await fetch(`${apiBase}/api/users/get-user`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: xToken }),
      })

      if (!res.ok) {
        throw new Error(`Failed to fetch user data: ${res.status}`)
      }

      const data = await res.json()
      if (data.success && data.user) {
        user.value = data.user
        // 更新本地存储
        localStorage.setItem('userState', JSON.stringify({
          isLoggedIn: true,
          credits: data.user.credits || 3,
          userId: data.user.id
        }))
      } else {
        console.error('Invalid user data received:', data)
        clearAuthState()
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
      clearAuthState()
    }
  }

  return {
    user,
    isLoggedIn,
    signOut,
    fetchUserData,
    clearAuthState
  }
} 